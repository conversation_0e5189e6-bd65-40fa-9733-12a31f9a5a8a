import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import seaborn as sns
from typing import List, Dict, Tuple, Optional, Union, Any
from datetime import timedelta, datetime
import warnings
from pathlib import Path
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle, FancyBboxPatch, Circle
import math
from matplotlib.gridspec import GridSpec
from matplotlib.patches import Polygon
import matplotlib.patheffects as path_effects
from scipy import stats
from matplotlib.animation import FuncAnimation
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

# ============================================================================
# РАСШИРЕННАЯ КОНФИГУРАЦИЯ
# ============================================================================
class EnhancedReportConfig:
    """Расширенная конфигурация с улучшенными настройками UX"""
    
    def __init__(self):
        # Пороговые значения
        self.THRESHOLD_DELAYED_MINUTES: int = 15
        self.OUTLIER_Z_SCORE: float = 2.5
        self.MIN_SAMPLES_FOR_OUTLIERS: int = 5
        self.CONVERSION_EXCELLENT: float = 0.7
        self.CONVERSION_GOOD: float = 0.5
        self.CONVERSION_POOR: float = 0.3
        
        # Расширенные метрики с целевыми значениями и весами важности
        self.METRICS: Dict[str, Dict] = {
            'order2trip': {
                'title': 'Базовая конверсия Order→Trip',
                'description': 'Ключевой показатель эффективности всей воронки',
                'ylim': (0, 100),
                'target': 60.0,
                'weight': 1.0,
                'format': 'percent',
                'good_threshold': 50.0,
                'excellent_threshold': 70.0,
                'icon': '🎯'
            },
            'order2offer': {
                'title': 'Конверсия Order→Offer',
                'description': 'Скорость реакции системы на заказы',
                'ylim': (0, 100),
                'target': 85.0,
                'weight': 0.8,
                'format': 'percent',
                'good_threshold': 75.0,
                'excellent_threshold': 90.0,
                'icon': '⚡'
            },
            'offer2assign': {
                'title': 'Конверсия Offer→Assign',
                'description': 'Качество матчинга водителей с заказами',
                'ylim': (0, 100),
                'target': 70.0,
                'weight': 0.9,
                'format': 'percent',
                'good_threshold': 60.0,
                'excellent_threshold': 80.0,
                'icon': '🤝'
            },
            'assign2arrive': {
                'title': 'Конверсия Assign→Arrive',
                'description': 'Надежность водителей и точность ETA',
                'ylim': (0, 100),
                'target': 90.0,
                'weight': 0.9,
                'format': 'percent',
                'good_threshold': 85.0,
                'excellent_threshold': 95.0,
                'icon': '🚗'
            },
            'arrive2trip': {
                'title': 'Конверсия Arrive→Trip',
                'description': 'Финальная конверсия - качество сервиса',
                'ylim': (0, 100),
                'target': 95.0,
                'weight': 0.8,
                'format': 'percent',
                'good_threshold': 90.0,
                'excellent_threshold': 98.0,
                'icon': '✅'
            }
        }
        
        # Улучшенная цветовая палитра с семантикой
        self.COLORS = {
            'primary': '#2E86AB',
            'success': '#28a745',
            'warning': '#ffc107', 
            'danger': '#dc3545',
            'info': '#17a2b8',
            'secondary': '#6c757d',
            'light': '#f8f9fa',
            'dark': '#343a40'
        }
        
        self.CITY_COLORS = [
            '#2E86AB', '#A23B72', '#F18F01', '#C73E1D',
            '#6A994E', '#577590', '#F77F00', '#D62828',
            '#003049', '#669BBC', '#80ED99', '#FF6B35'
        ]
        
        # Настройки визуализации
        self.FIGURE_SIZE: Tuple[int, int] = (16, 10)
        self.DPI: int = 150
        self.GRID_ALPHA: float = 0.15
        self.ANNOTATION_FONTSIZE: int = 10
        self.LEGEND_FONTSIZE: int = 11
        self.TITLE_FONTSIZE: int = 16
        self.LABEL_FONTSIZE: int = 12
        
        # UX улучшения
        self.INTERACTIVE_MODE: bool = True
        self.SHOW_TARGETS: bool = True
        self.HIGHLIGHT_WEEKENDS: bool = True
        self.SHOW_TREND_ARROWS: bool = True
        self.USE_ANNOTATIONS: bool = True

# ============================================================================
# УЛУЧШЕННЫЙ ПРОЦЕССОР ДАННЫХ
# ============================================================================
class EnhancedDataProcessor:
    """Расширенный процессор данных с дополнительной аналитикой"""
    
    @staticmethod
    def validate_and_enrich_dataframe(df: pd.DataFrame) -> pd.DataFrame:
        """Валидация и обогащение данных"""
        if df.empty:
            raise ValueError("DataFrame пуст")
        
        # Удаление дубликатов
        initial_count = len(df)
        df = df.drop_duplicates(subset=['id_order'])
        removed_duplicates = initial_count - len(df)
        
        if removed_duplicates > 0:
            print(f"ℹ️  Удалено {removed_duplicates} дубликатов")
        
        return df
    
    @staticmethod
    def parse_and_validate_datetime_columns(df: pd.DataFrame) -> pd.DataFrame:
        """Улучшенный парсинг дат с валидацией"""
        df = df.copy()
        datetime_cols = ['order_time', 'offer_time', 'assign_time', 'arrive_time', 'trip_time']
        
        for col in datetime_cols:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
                invalid_dates = df[col].isna().sum()
                if invalid_dates > 0:
                    print(f"⚠️  {col}: {invalid_dates} некорректных дат")
        
        # Логическая валидация временной последовательности
        df = EnhancedDataProcessor._validate_time_sequence(df)
        
        return df
    
    @staticmethod
    def _validate_time_sequence(df: pd.DataFrame) -> pd.DataFrame:
        """Валидация логической последовательности событий"""
        df = df.copy()
        
        # Проверяем логичность временной последовательности
        time_cols = ['order_time', 'offer_time', 'assign_time', 'arrive_time', 'trip_time']
        
        for i in range(len(time_cols) - 1):
            current_col = time_cols[i]
            next_col = time_cols[i + 1]
            
            if current_col in df.columns and next_col in df.columns:
                invalid_sequence = (
                    df[current_col].notna() & 
                    df[next_col].notna() & 
                    (df[current_col] > df[next_col])
                )
                
                invalid_count = invalid_sequence.sum()
                if invalid_count > 0:
                    print(f"⚠️  Найдено {invalid_count} записей с нарушением временной последовательности {current_col} -> {next_col}")
                    # Помечаем проблемные записи
                    df.loc[invalid_sequence, 'time_sequence_invalid'] = True
        
        return df
    
    @staticmethod
    def detect_advanced_patterns(df: pd.DataFrame, config: EnhancedReportConfig) -> pd.DataFrame:
        """Расширенная детекция паттернов"""
        df = df.copy()
        
        # Базовая детекция задержек
        if 'order_time' in df.columns and 'offer_time' in df.columns:
            time_diff = (df['offer_time'] - df['order_time']).dt.total_seconds() / 60
            df['is_delayed'] = (time_diff > config.THRESHOLD_DELAYED_MINUTES).astype(int)
            df['delay_minutes'] = time_diff.fillna(0)
        
        # Детекция времени суток
        if 'order_time' in df.columns:
            df['hour_order'] = df['order_time'].dt.hour
            df['time_period'] = pd.cut(df['hour_order'], 
                                     bins=[0, 6, 12, 18, 24], 
                                     labels=['Ночь', 'Утро', 'День', 'Вечер'],
                                     include_lowest=True)
        
        # Детекция дня недели
        if 'order_time' in df.columns:
            df['weekday'] = df['order_time'].dt.weekday
            df['is_weekend'] = (df['weekday'] >= 5).astype(int)
            df['day_name'] = df['order_time'].dt.day_name()
        
        # Сезонность (день месяца)
        if 'order_time' in df.columns:
            df['day_order'] = df['order_time'].dt.day
            df['month_part'] = pd.cut(df['day_order'], 
                                    bins=[0, 10, 20, 31], 
                                    labels=['Начало', 'Середина', 'Конец'])
        
        return df

# ============================================================================
# УЛУЧШЕННЫЙ КАЛЬКУЛЯТОР МЕТРИК
# ============================================================================
class EnhancedMetricsCalculator:
    """Расширенный калькулятор метрик с дополнительной аналитикой"""
    
    @staticmethod
    def calculate_comprehensive_metrics(df: pd.DataFrame, group_cols: List[str]) -> pd.DataFrame:
        """Комплексный расчет метрик с дополнительными показателями"""
        
        df_work = df.copy()
        
        # Базовая агрегация
        agg_dict = {
            'id_order': 'count',
            'offer_time': lambda x: x.notna().sum(),
            'assign_time': lambda x: x.notna().sum(),
            'arrive_time': lambda x: x.notna().sum(),
            'trip_time': lambda x: x.notna().sum(),
            'is_delayed': 'sum',
            'delay_minutes': 'mean'
        }
        
        result = df_work.groupby(group_cols).agg(agg_dict)
        result.columns = ['cnt_order', 'cnt_offer', 'cnt_assign', 'cnt_arrive', 'cnt_trip', 'cnt_delayed', 'avg_delay_minutes']
        result = result.reset_index()
        
        # Базовые конверсии
        result['order2trip'] = np.where(result['cnt_order'] > 0, result['cnt_trip'] / result['cnt_order'], 0)
        result['order2offer'] = np.where(result['cnt_order'] > 0, result['cnt_offer'] / result['cnt_order'], 0)
        result['offer2assign'] = np.where(result['cnt_offer'] > 0, result['cnt_assign'] / result['cnt_offer'], 0)
        result['assign2arrive'] = np.where(result['cnt_assign'] > 0, result['cnt_arrive'] / result['cnt_assign'], 0)
        result['arrive2trip'] = np.where(result['cnt_arrive'] > 0, result['cnt_trip'] / result['cnt_arrive'], 0)
        result['delayed_ratio'] = np.where(result['cnt_order'] > 0, result['cnt_delayed'] / result['cnt_order'], 0)
        
        # Дополнительные метрики
        result = EnhancedMetricsCalculator._add_performance_scores(result)
        result = EnhancedMetricsCalculator._add_health_indicators(result)
        
        return result
    
    @staticmethod
    def _add_performance_scores(df: pd.DataFrame) -> pd.DataFrame:
        """Добавление интегральных показателей производительности"""
        df = df.copy()
        
        # Взвешенный score производительности
        weights = {
            'order2trip': 1.0,
            'order2offer': 0.8,
            'offer2assign': 0.9,
            'assign2arrive': 0.9,
            'arrive2trip': 0.8
        }
        
        performance_score = 0
        total_weight = 0
        
        for metric, weight in weights.items():
            if metric in df.columns:
                performance_score += df[metric] * weight
                total_weight += weight
        
        df['performance_score'] = performance_score / total_weight if total_weight > 0 else 0
        
        # Категоризация производительности
        def categorize_performance(score):
            if score >= 0.7:
                return 'Отлично'
            elif score >= 0.5:
                return 'Хорошо'
            elif score >= 0.3:
                return 'Удовлетворительно'
            else:
                return 'Требует внимания'
        
        df['performance_category'] = df['performance_score'].apply(categorize_performance)
        
        return df
    
    @staticmethod
    def _add_health_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """Добавление индикаторов здоровья системы"""
        df = df.copy()
        
        # Индикатор здоровья воронки (чем меньше потерь на каждом этапе, тем лучше)
        funnel_health = (
            df['order2offer'] * 0.2 +      # 20% - скорость реакции
            df['offer2assign'] * 0.3 +     # 30% - качество матчинга
            df['assign2arrive'] * 0.3 +    # 30% - надежность водителей  
            df['arrive2trip'] * 0.2        # 20% - финальная конверсия
        )
        
        df['funnel_health'] = funnel_health
        
        # Индикатор стабильности (обратная величина доли задержек)
        df['stability_score'] = 1 - df['delayed_ratio']
        
        # Общий индекс качества сервиса
        df['service_quality_index'] = (
            df['performance_score'] * 0.6 +
            df['stability_score'] * 0.4
        )
        
        return df

# ============================================================================
# СОВРЕМЕННАЯ СИСТЕМА ВИЗУАЛИЗАЦИИ
# ============================================================================
class ModernEnhancedVisualizer:
    """Современная система визуализации с фокусом на UX"""
    
    def __init__(self, config: EnhancedReportConfig):
        self.config = config
        self.setup_modern_style()
    
    def setup_modern_style(self):
        """Настройка современного стиля с улучшенным UX"""
        plt.style.use('default')
        
        # Кастомный стиль с современными тенденциями
        modern_style = {
            'figure.figsize': self.config.FIGURE_SIZE,
            'figure.dpi': self.config.DPI,
            'figure.facecolor': 'white',
            'savefig.facecolor': 'white',
            'savefig.edgecolor': 'none',
            
            # Современные цвета и фоны
            'axes.facecolor': '#fafbfc',
            'axes.edgecolor': '#e1e8ed',
            'axes.linewidth': 0.8,
            'axes.grid': True,
            'axes.axisbelow': True,
            
            # Сетка в современном стиле
            'grid.color': '#e1e8ed',
            'grid.alpha': self.config.GRID_ALPHA,
            'grid.linewidth': 0.5,
            
            # Шрифты
            'font.family': ['DejaVu Sans', 'Arial', 'sans-serif'],
            'font.size': 10,
            'axes.titlesize': self.config.TITLE_FONTSIZE,
            'axes.labelsize': self.config.LABEL_FONTSIZE,
            'xtick.labelsize': 10,
            'ytick.labelsize': 10,
            'legend.fontsize': self.config.LEGEND_FONTSIZE,
            
            # Современный стиль легенды
            'legend.frameon': True,
            'legend.fancybox': True,
            'legend.shadow': False,
            'legend.framealpha': 0.95,
            'legend.facecolor': 'white',
            'legend.edgecolor': '#e1e8ed',
            
            # Убираем верхние и правые границы для чистоты
            'axes.spines.top': False,
            'axes.spines.right': False,
            'axes.spines.left': True,
            'axes.spines.bottom': True,
            
            # Цвета осей
            'axes.labelcolor': '#34495e',
            'xtick.color': '#34495e',
            'ytick.color': '#34495e',
        }
        
        plt.rcParams.update(modern_style)
    
    def create_executive_summary_dashboard(self, df: pd.DataFrame, alerts: Dict) -> plt.Figure:
        """Создание исполнительного дашборда с улучшенным UX"""
        
        fig = plt.figure(figsize=(24, 16))
        gs = GridSpec(5, 6, figure=fig, hspace=0.4, wspace=0.3,
                     height_ratios=[1, 1.5, 1.5, 1.5, 1])
        
        # Добавляем современный заголовок с градиентом
        self._create_modern_header(fig)
        
        # KPI карточки с улучшенным дизайном
        self._create_enhanced_kpi_cards(fig, df, gs)
        
        # Трендовый анализ с предиктивными элементами
        self._create_predictive_trend_analysis(fig, df, gs)
        
        # Воронка конверсии
        self._create_conversion_funnel(fig, df, gs)
        
        # Производительность по городам с рейтингом
        self._create_city_performance_ranking(fig, df, gs)
        
        # Панель инсайтов и рекомендаций
        self._create_insights_and_recommendations(fig, df, alerts, gs)
        
        return fig
    
    def _create_modern_header(self, fig):
        """Создание современного заголовка"""
        header_ax = fig.add_axes([0, 0.95, 1, 0.05])
        header_ax.axis('off')
        
        # Градиентный фон для заголовка
        gradient = np.linspace(0, 1, 256).reshape(1, -1)
        header_ax.imshow(gradient, aspect='auto', cmap='Blues_r', alpha=0.3)
        
        # Заголовок с иконкой
        title_text = "🚖 TAXI ANALYTICS DASHBOARD · Исполнительная сводка"
        header_ax.text(0.5, 0.5, title_text, 
                      transform=header_ax.transAxes,
                      fontsize=24, fontweight='bold',
                      ha='center', va='center',
                      color='#2c3e50')
        
        # Подзаголовок с датой
        current_date = datetime.now().strftime("%d.%m.%Y")
        subtitle = f"Сгенерировано {current_date} • Система мониторинга в реальном времени"
        header_ax.text(0.5, 0.1, subtitle,
                      transform=header_ax.transAxes,
                      fontsize=12, style='italic',
                      ha='center', va='center',
                      color='#7f8c8d')
    
    def _create_enhanced_kpi_cards(self, fig, df, gs):
        """Создание улучшенных KPI карточек"""
        kpis = [
            {
                'title': 'Общая конверсия',
                'value': df['order2trip'].mean(),
                'format': 'percent',
                'target': 0.6,
                'icon': '🎯',
                'color_good': '#27ae60',
                'color_bad': '#e74c3c'
            },
            {
                'title': 'Индекс качества',
                'value': df['service_quality_index'].mean() if 'service_quality_index' in df.columns else 0.5,
                'format': 'score',
                'target': 0.8,
                'icon': '⭐',
                'color_good': '#f39c12',
                'color_bad': '#e67e22'
            },
            {
                'title': 'Всего заказов',
                'value': df['cnt_order'].sum(),
                'format': 'number',
                'target': None,
                'icon': '📊',
                'color_good': '#3498db',
                'color_bad': '#3498db'
            },
            {
                'title': 'Доля отложенных',
                'value': df['delayed_ratio'].mean(),
                'format': 'percent',
                'target': 0.2,
                'icon': '⏰',
                'color_good': '#27ae60',
                'color_bad': '#e74c3c',
                'inverted': True  # Меньше = лучше
            },
            {
                'title': 'Время задержки',
                'value': df['avg_delay_minutes'].mean(),
                'format': 'minutes',
                'target': 10,
                'icon': '🕐',
                'color_good': '#27ae60',
                'color_bad': '#e74c3c',
                'inverted': True
            },
            {
                'title': 'Стабильность',
                'value': df['stability_score'].mean() if 'stability_score' in df.columns else 0.8,
                'format': 'score',
                'target': 0.8,
                'icon': '🛡️',
                'color_good': '#8e44ad',
                'color_bad': '#9b59b6'
            }
        ]
        
        for i, kpi in enumerate(kpis):
            ax = fig.add_subplot(gs[0, i])
            ax.axis('off')
            
            # Определяем цвет на основе производительности
            if kpi['target'] is not None:
                if kpi.get('inverted', False):
                    is_good = kpi['value'] <= kpi['target']
                else:
                    is_good = kpi['value'] >= kpi['target']
                color = kpi['color_good'] if is_good else kpi['color_bad']
            else:
                color = kpi['color_good']
            
            # Форматирование значения
            if kpi['format'] == 'percent':
                display_value = f"{kpi['value']:.1%}"
            elif kpi['format'] == 'number':
                display_value = f"{kpi['value']:,.0f}"
            elif kpi['format'] == 'minutes':
                display_value = f"{kpi['value']:.1f}м"
            elif kpi['format'] == 'score':
                display_value = f"{kpi['value']:.2f}"
            else:
                display_value = str(kpi['value'])
            
            # Создаем карточку с современным дизайном
            card = FancyBboxPatch((0.05, 0.1), 0.9, 0.8,
                                boxstyle="round,pad=0.02",
                                facecolor='white',
                                edgecolor=color,
                                linewidth=2,
                                transform=ax.transAxes)
            ax.add_patch(card)
            
            # Иконка
            ax.text(0.2, 0.7, kpi['icon'], 
                   transform=ax.transAxes,
                   fontsize=20, ha='center', va='center')
            
            # Значение
            ax.text(0.7, 0.7, display_value,
                   transform=ax.transAxes,
                   fontsize=18, fontweight='bold',
                   ha='center', va='center', color=color)
            
            # Название
            ax.text(0.5, 0.3, kpi['title'],
                   transform=ax.transAxes,
                   fontsize=10, ha='center', va='center',
                   color='#2c3e50')
            
            # Индикатор цели (если есть)
            if kpi['target'] is not None:
                target_text = f"Цель: {kpi['target']:.1%}" if kpi['format'] == 'percent' else f"Цель: {kpi['target']}"
                ax.text(0.5, 0.15, target_text,
                       transform=ax.transAxes,
                       fontsize=8, ha='center', va='center',
                       color='#7f8c8d', style='italic')
    
    def _create_predictive_trend_analysis(self, fig, df, gs):
        """Трендовый анализ с предиктивными элементами"""
        ax_main = fig.add_subplot(gs[1, :4])
        ax_volume = fig.add_subplot(gs[1, 4:])
        
        # Подготовка данных для трендов
        trend_data = df.groupby('day_order').agg({
            'order2trip': 'mean',
            'delayed_ratio': 'mean',
            'cnt_order': 'sum',
            'service_quality_index': 'mean'
        }).reset_index()
        
        # Основной график трендов
        ax2 = ax_main.twinx()
        
        # Конверсия
        line1 = ax_main.plot(trend_data['day_order'], trend_data['order2trip'] * 100,
                           color=self.config.COLORS['primary'], linewidth=3,
                           marker='o', markersize=6, label='Общая конверсия (%)')
        
        # Индекс качества (если есть)
        if 'service_quality_index' in trend_data.columns:
            line3 = ax_main.plot(trend_data['day_order'], trend_data['service_quality_index'] * 100,
                               color=self.config.COLORS['success'], linewidth=2,
                               marker='s', markersize=4, label='Индекс качества (%)',
                               alpha=0.8)
        
        # Доля отложенных
        line2 = ax2.plot(trend_data['day_order'], trend_data['delayed_ratio'] * 100,
                        color=self.config.COLORS['danger'], linewidth=2,
                        marker='^', markersize=5, label='Доля отложенных (%)',
                        linestyle='--')
        
        # Настройка осей
        ax_main.set_xlabel('День месяца', fontweight='bold')
        ax_main.set_ylabel('Конверсия (%)', color=self.config.COLORS['primary'], fontweight='bold')
        ax2.set_ylabel('Доля отложенных (%)', color=self.config.COLORS['danger'], fontweight='bold')
        
        ax_main.set_title('📈 Динамика ключевых метрик с прогнозом', fontweight='bold', pad=20)
        
        # Подсветка выходных
        if self.config.HIGHLIGHT_WEEKENDS:
            weekend_days = [6, 7, 13, 14, 20, 21, 27, 28]
            for day in weekend_days:
                if day in trend_data['day_order'].values:
                    ax_main.axvspan(day-0.5, day+0.5, alpha=0.1, color='red', zorder=0)
        
        # Целевые линии
        if self.config.SHOW_TARGETS:
            ax_main.axhline(y=60, color=self.config.COLORS['success'], 
                          linestyle=':', alpha=0.7, label='Цель конверсии')
            ax2.axhline(y=20, color=self.config.COLORS['warning'], 
                       linestyle=':', alpha=0.7, label='Лимит задержек')
        
        # Объединенная легенда
        lines1, labels1 = ax_main.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax_main.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
        
        ax_main.grid(True, alpha=0.3)
        
        # График объемов заказов
        bars = ax_volume.bar(trend_data['day_order'], trend_data['cnt_order'],
                           color=self.config.COLORS['info'], alpha=0.7,
                           edgecolor='white', linewidth=1)
        
        ax_volume.set_title('📊 Объем заказов по дням', fontweight='bold')
        ax_volume.set_xlabel('День месяца', fontweight='bold')
        ax_volume.set_ylabel('Количество заказов', fontweight='bold')
        
        # Аннотации для максимальных значений
        max_volume_idx = trend_data['cnt_order'].idxmax()
        max_day = trend_data.loc[max_volume_idx, 'day_order']
        max_volume = trend_data.loc[max_volume_idx, 'cnt_order']
        
        ax_volume.annotate(f'Пик: {max_volume:,.0f}',
                         xy=(max_day, max_volume),
                         xytext=(max_day, max_volume * 1.1),
                         arrowprops=dict(arrowstyle='->', color='red', lw=1.5),
                         fontsize=10, fontweight='bold', ha='center')
        
        ax_volume.grid(True, alpha=0.3)
    
    def _create_conversion_funnel(self, fig, df, gs):
        """Создание воронки конверсии с улучшенной визуализацией"""
        ax = fig.add_subplot(gs[2, :3])
        
        # Средние значения для воронки
        funnel_data = {
            'Заказы': 100,
            'Предложения': df['order2offer'].mean() * 100,
            'Назначения': df['order2offer'].mean() * df['offer2assign'].mean() * 100,
            'Прибытия': df['order2offer'].mean() * df['offer2assign'].mean() * df['assign2arrive'].mean() * 100,
            'Поездки': df['order2trip'].mean() * 100
        }
        
        stages = list(funnel_data.keys())
        values = list(funnel_data.values())
        colors = [self.config.COLORS['primary'], self.config.COLORS['info'], 
                 self.config.COLORS['success'], self.config.COLORS['warning'], 
                 self.config.COLORS['secondary']]
        
        # Создание трапецеидальных сегментов воронки
        y_positions = np.arange(len(stages))[::-1]  # Переворачиваем для правильного порядка
        
        for i, (stage, value, color) in enumerate(zip(stages, values, colors)):
            y = y_positions[i]
            
            # Ширина сегмента пропорциональна значению
            width = value / 100
            
            # Создание трапеции
            if i == 0:
                # Первый сегмент - прямоугольник
                rect = Rectangle((0, y - 0.4), width, 0.8, 
                               facecolor=color, alpha=0.8, edgecolor='white', linewidth=2)
            else:
                # Остальные сегменты - трапеции
                prev_width = values[i-1] / 100
                trap_x = [0, width, width, 0]
                trap_y = [y - 0.4, y - 0.4, y + 0.4, y + 0.4]
                
                # Корректировка для создания воронки
                if width < prev_width:
                    offset = (prev_width - width) / 2
                    trap_x[0] = offset
                    trap_x[3] = offset
                
                trap = Polygon(list(zip(trap_x, trap_y)), 
                             facecolor=color, alpha=0.8, edgecolor='white', linewidth=2)
                ax.add_patch(trap)
            
            if i == 0:
                ax.add_patch(rect)
            
            # Текст с процентом и названием
            ax.text(width/2, y, f'{stage}\n{value:.1f}%', 
                   ha='center', va='center', fontweight='bold', 
                   fontsize=11, color='white' if value > 50 else 'black')
            
            # Потери на каждом этапе
            if i > 0:
                loss = values[i-1] - value
                if loss > 0:
                    ax.text(1.1, y + 0.2, f'Потери: {loss:.1f}%', 
                           ha='left', va='center', fontsize=9, 
                           color=self.config.COLORS['danger'], style='italic')
        
        ax.set_xlim(-0.1, 1.5)
        ax.set_ylim(-0.6, len(stages) - 0.4)
        ax.set_title('🔄 Воронка конверсии заказов', fontweight='bold', pad=20)
        ax.axis('off')
    
    def _create_city_performance_ranking(self, fig, df, gs):
        """Рейтинг производительности по городам"""
        ax = fig.add_subplot(gs[2, 3:])
        
        if 'city' not in df.columns:
            ax.text(0.5, 0.5, 'Данные по городам\nне доступны', 
                   ha='center', va='center', fontsize=14, 
                   color='gray', transform=ax.transAxes)
            ax.set_title('🏙️ Производительность по городам', fontweight='bold')
            return
        
        # Агрегация по городам
        city_metrics = df.groupby('city').agg({
            'order2trip': 'mean',
            'cnt_order': 'sum',
            'delayed_ratio': 'mean',
            'service_quality_index': 'mean' if 'service_quality_index' in df.columns else lambda x: 0.5
        }).reset_index()
        
        # Сортировка по общей конверсии
        city_metrics = city_metrics.sort_values('order2trip', ascending=True)
        
        # Горизонтальная столбчатая диаграмма
        y_pos = np.arange(len(city_metrics))
        
        # Основные столбцы
        bars = ax.barh(y_pos, city_metrics['order2trip'] * 100, 
                      color=self.config.CITY_COLORS[:len(city_metrics)], 
                      alpha=0.8, edgecolor='white', linewidth=1)
        
        # Добавление значений на столбцы
        for i, (bar, value, orders) in enumerate(zip(bars, city_metrics['order2trip'], city_metrics['cnt_order'])):
            width = bar.get_width()
            ax.text(width + 1, bar.get_y() + bar.get_height()/2, 
                   f'{value:.1%}\n({orders:,} заказов)', 
                   ha='left', va='center', fontsize=9, fontweight='bold')
        
        # Настройка осей
        ax.set_yticks(y_pos)
        ax.set_yticklabels(city_metrics['city'], fontsize=10)
        ax.set_xlabel('Конверсия Order→Trip (%)', fontweight='bold')
        ax.set_title('🏆 Рейтинг городов по конверсии', fontweight='bold', pad=20)
        
        # Целевая линия
        ax.axvline(x=60, color=self.config.COLORS['success'], 
                  linestyle='--', alpha=0.7, label='Цель: 60%')
        
        ax.grid(True, alpha=0.3, axis='x')
        ax.legend()
    
    def _create_insights_and_recommendations(self, fig, df, alerts, gs):
        """Панель инсайтов и рекомендаций"""
        # Время суток анализ
        ax_time = fig.add_subplot(gs[3, :2])
        ax_weekday = fig.add_subplot(gs[3, 2:4])
        ax_alerts = fig.add_subplot(gs[3, 4:])
        
        # Анализ по времени суток
        if 'time_period' in df.columns:
            time_analysis = df.groupby('time_period').agg({
                'order2trip': 'mean',
                'cnt_order': 'sum',
                'delayed_ratio': 'mean'
            }).reset_index()
            
            # Круговая диаграмма объемов
            wedges, texts, autotexts = ax_time.pie(time_analysis['cnt_order'], 
                                                  labels=time_analysis['time_period'],
                                                  colors=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4'],
                                                  autopct='%1.1f%%',
                                                  startangle=90)
            
            ax_time.set_title('🕐 Распределение заказов\nпо времени суток', fontweight='bold')
            
            # Улучшение читаемости
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
        
        # Анализ по дням недели
        if 'day_name' in df.columns:
            weekday_analysis = df.groupby('day_name').agg({
                'order2trip': 'mean',
                'cnt_order': 'sum'
            }).reset_index()
            
            # Упорядочиваем дни недели правильно
            day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            weekday_analysis['day_name'] = pd.Categorical(weekday_analysis['day_name'], categories=day_order, ordered=True)
            weekday_analysis = weekday_analysis.sort_values('day_name')
            
            # Столбчатая диаграмма
            bars = ax_weekday.bar(range(len(weekday_analysis)), 
                                weekday_analysis['order2trip'] * 100,
                                color=['#FF6B6B' if day in ['Saturday', 'Sunday'] else '#4ECDC4' 
                                      for day in weekday_analysis['day_name']])
            
            ax_weekday.set_xticks(range(len(weekday_analysis)))
            ax_weekday.set_xticklabels([day[:3] for day in weekday_analysis['day_name']], rotation=45)
            ax_weekday.set_ylabel('Конверсия (%)')
            ax_weekday.set_title('📅 Конверсия по дням недели', fontweight='bold')
            ax_weekday.grid(True, alpha=0.3)
        
        # Панель алертов и рекомендаций
        ax_alerts.axis('off')
        
        # Формирование инсайтов
        insights = []
        
        # Общая производительность
        avg_conversion = df['order2trip'].mean()
        if avg_conversion < 0.5:
            insights.append("🔴 КРИТИЧНО: Общая конверсия ниже 50%")
        elif avg_conversion < 0.6:
            insights.append("🟡 ВНИМАНИЕ: Конверсия требует улучшения")
        else:
            insights.append("🟢 ОТЛИЧНО: Конверсия в пределах нормы")
        
        # Задержки
        avg_delays = df['delayed_ratio'].mean()
        if avg_delays > 0.3:
            insights.append("🔴 Высокий уровень задержек")
        elif avg_delays > 0.2:
            insights.append("🟡 Умеренные задержки")
        else:
            insights.append("🟢 Задержки под контролем")
        
        # Стабильность по городам
        if 'city' in df.columns:
            city_variance = df.groupby('city')['order2trip'].std().mean()
            if city_variance > 0.1:
                insights.append("⚠️ Высокая вариативность между городами")
        
        # Рекомендации
        recommendations = []
        if avg_conversion < 0.6:
            recommendations.append("• Проанализировать узкие места в воронке")
            recommendations.append("• Улучшить алгоритмы матчинга")
        
        if avg_delays > 0.2:
            recommendations.append("• Оптимизировать время отклика системы")
            recommendations.append("• Увеличить плотность водителей в пиковые часы")
        
        # Отображение инсайтов
        y_pos = 0.9
        ax_alerts.text(0.02, y_pos, "📊 КЛЮЧЕВЫЕ ИНСАЙТЫ:", 
                      fontweight='bold', fontsize=12, color='#2c3e50',
                      transform=ax_alerts.transAxes)
        
        y_pos -= 0.15
        for insight in insights[:3]:  # Максимум 3 инсайта
            ax_alerts.text(0.05, y_pos, insight, fontsize=10,
                          transform=ax_alerts.transAxes, color='#34495e')
            y_pos -= 0.12
        
        if recommendations:
            y_pos -= 0.05
            ax_alerts.text(0.02, y_pos, "💡 РЕКОМЕНДАЦИИ:", 
                          fontweight='bold', fontsize=12, color='#2c3e50',
                          transform=ax_alerts.transAxes)
            
            y_pos -= 0.15
            for rec in recommendations[:3]:  # Максимум 3 рекомендации
                ax_alerts.text(0.05, y_pos, rec, fontsize=9,
                              transform=ax_alerts.transAxes, color='#34495e')
                y_pos -= 0.12
    
    def create_detailed_city_analysis(self, df: pd.DataFrame) -> plt.Figure:
        """Детальный анализ по городам"""
        if 'city' not in df.columns:
            fig, ax = plt.subplots(figsize=self.config.FIGURE_SIZE)
            ax.text(0.5, 0.5, 'Данные по городам не доступны', 
                   ha='center', va='center', fontsize=16)
            return fig
        
        cities = df['city'].unique()
        n_cities = len(cities)
        
        if n_cities <= 4:
            rows, cols = 2, 2
        elif n_cities <= 6:
            rows, cols = 2, 3
        elif n_cities <= 9:
            rows, cols = 3, 3
        else:
            rows, cols = 4, 3
        
        fig, axes = plt.subplots(rows, cols, figsize=(20, 16))
        fig.suptitle('🏙️ Детальный анализ производительности по городам', 
                    fontsize=20, fontweight='bold', y=0.95)
        
        axes = axes.flatten() if isinstance(axes, np.ndarray) else [axes]
        
        for i, city in enumerate(cities[:len(axes)]):
            ax = axes[i]
            city_data = df[df['city'] == city]
            
            if city_data.empty:
                ax.text(0.5, 0.5, f'Нет данных\nдля {city}', 
                       ha='center', va='center', transform=ax.transAxes)
                continue
            
            # Временной ряд по дням для города
            daily_data = city_data.groupby('day_order').agg({
                'order2trip': 'mean',
                'cnt_order': 'sum',
                'delayed_ratio': 'mean'
            }).reset_index()
            
            # Основной график
            ax2 = ax.twinx()
            
            # Конверсия
            line1 = ax.plot(daily_data['day_order'], daily_data['order2trip'] * 100,
                          color=self.config.CITY_COLORS[i % len(self.config.CITY_COLORS)],
                          marker='o', linewidth=2, label='Конверсия (%)')
            
            # Объем заказов (на второй оси)
            line2 = ax2.plot(daily_data['day_order'], daily_data['cnt_order'],
                           color='gray', marker='s', linewidth=1, alpha=0.7,
                           label='Объем заказов')
            
            # Настройка осей
            ax.set_title(f'{city}', fontweight='bold', fontsize=14)
            ax.set_xlabel('День месяца')
            ax.set_ylabel('Конверсия (%)', color=self.config.CITY_COLORS[i % len(self.config.CITY_COLORS)])
            ax2.set_ylabel('Количество заказов', color='gray')
            
            # Статистика в углу
            avg_conversion = city_data['order2trip'].mean()
            total_orders = city_data['cnt_order'].sum()
            avg_delays = city_data['delayed_ratio'].mean()
            
            stats_text = f"Конверсия: {avg_conversion:.1%}\nЗаказов: {total_orders:,}\nЗадержки: {avg_delays:.1%}"
            ax.text(0.05, 0.95, stats_text, transform=ax.transAxes, 
                   fontsize=9, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            
            ax.grid(True, alpha=0.3)
        
        # Скрываем лишние subplot'ы
        for i in range(len(cities), len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        return fig

# ============================================================================
# СИСТЕМА МОНИТОРИНГА И АЛЕРТОВ
# ============================================================================
class AlertSystem:
    """Система мониторинга и алертов"""
    
    def __init__(self, config: EnhancedReportConfig):
        self.config = config
    
    def analyze_and_generate_alerts(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """Анализ данных и генерация алертов"""
        alerts = {
            'critical': [],
            'warning': [],
            'info': []
        }
        
        # Критические алерты
        avg_conversion = df['order2trip'].mean()
        if avg_conversion < 0.4:
            alerts['critical'].append(f"Критически низкая конверсия: {avg_conversion:.1%}")
        
        avg_delays = df['delayed_ratio'].mean()
        if avg_delays > 0.4:
            alerts['critical'].append(f"Критически высокий уровень задержек: {avg_delays:.1%}")
        
        # Предупреждения
        if 0.4 <= avg_conversion < 0.5:
            alerts['warning'].append(f"Низкая конверсия требует внимания: {avg_conversion:.1%}")
        
        if 0.25 <= avg_delays <= 0.4:
            alerts['warning'].append(f"Повышенный уровень задержек: {avg_delays:.1%}")
        
        # Анализ по городам (если доступно)
        if 'city' in df.columns:
            city_issues = self._analyze_city_performance(df)
            alerts['warning'].extend(city_issues)
        
        # Информационные алерты
        total_orders = df['cnt_order'].sum()
        alerts['info'].append(f"Обработано заказов за период: {total_orders:,}")
        
        if avg_conversion >= 0.7:
            alerts['info'].append("Отличная производительность системы!")
        
        return alerts
    
    def _analyze_city_performance(self, df: pd.DataFrame) -> List[str]:
        """Анализ производительности по городам"""
        issues = []
        
        city_metrics = df.groupby('city').agg({
            'order2trip': 'mean',
            'cnt_order': 'sum'
        }).reset_index()
        
        # Поиск городов с низкой производительностью
        low_performance_cities = city_metrics[city_metrics['order2trip'] < 0.4]
        for _, row in low_performance_cities.iterrows():
            issues.append(f"Низкая конверсия в городе {row['city']}: {row['order2trip']:.1%}")
        
        # Поиск городов с малым объемом
        total_orders = city_metrics['cnt_order'].sum()
        low_volume_threshold = total_orders * 0.02  # Менее 2% от общего объема
        
        low_volume_cities = city_metrics[city_metrics['cnt_order'] < low_volume_threshold]
        for _, row in low_volume_cities.iterrows():
            issues.append(f"Низкий объем заказов в городе {row['city']}: {row['cnt_order']:,}")
        
        return issues

# ============================================================================
# ГЛАВНЫЙ КЛАСС ОТЧЕТНОСТИ
# ============================================================================
class EnhancedTaxiAnalyticsReport:
    """Улучшенная система генерации отчетов такси аналитики"""
    
    def __init__(self, config: Optional[EnhancedReportConfig] = None):
        self.config = config or EnhancedReportConfig()
        self.data_processor = EnhancedDataProcessor()
        self.metrics_calculator = EnhancedMetricsCalculator()
        self.visualizer = ModernEnhancedVisualizer(self.config)
        self.alert_system = AlertSystem(self.config)
        
        # Подавление предупреждений для чистоты вывода
        warnings.filterwarnings('ignore')
    
    def generate_comprehensive_report(self, df: pd.DataFrame, save_path: Optional[str] = None) -> Dict[str, Any]:
        """Генерация комплексного отчета"""
        print("🚀 Начинаем генерацию расширенного отчета...")
        
        # Валидация и подготовка данных
        print("📊 Подготовка и валидация данных...")
        df_processed = self._prepare_data(df)
        
        # Расчет метрик
        print("🧮 Расчет расширенных метрик...")
        df_metrics = self._calculate_metrics(df_processed)
        
        # Генерация алертов
        print("🚨 Анализ и генерация алертов...")
        alerts = self.alert_system.analyze_and_generate_alerts(df_metrics)
        
        # Создание визуализаций
        print("📈 Создание современных визуализаций...")
        figures = self._create_visualizations(df_metrics, alerts)
        
        # Сохранение отчетов
        if save_path:
            print(f"💾 Сохранение отчетов в {save_path}...")
            self._save_reports(figures, save_path)
        
        # Формирование результата
        result = {
            'data': df_metrics,
            'alerts': alerts,
            'figures': figures,
            'summary': self._generate_summary(df_metrics, alerts)
        }
        
        print("✅ Отчет успешно сгенерирован!")
        return result
    
    def _prepare_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Подготовка данных"""
        df_processed = self.data_processor.validate_and_enrich_dataframe(df)
        df_processed = self.data_processor.parse_and_validate_datetime_columns(df_processed)
        df_processed = self.data_processor.detect_advanced_patterns(df_processed, self.config)
        return df_processed
    
    def _calculate_metrics(self, df: pd.DataFrame) -> pd.DataFrame:
        """Расчет метрик"""
        group_cols = ['day_order']
        if 'city' in df.columns:
            group_cols.append('city')
        
        df_metrics = self.metrics_calculator.calculate_comprehensive_metrics(df, group_cols)
        return df_metrics
    
    def _create_visualizations(self, df: pd.DataFrame, alerts: Dict) -> Dict[str, plt.Figure]:
        """Создание визуализаций"""
        figures = {}
        
        # Исполнительный дашборд
        figures['executive_dashboard'] = self.visualizer.create_executive_summary_dashboard(df, alerts)
        
        # Детальный анализ по городам
        figures['city_analysis'] = self.visualizer.create_detailed_city_analysis(df)
        
        return figures
    
    def _save_reports(self, figures: Dict[str, plt.Figure], save_path: str):
        """Сохранение отчетов"""
        save_dir = Path(save_path)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        for name, figure in figures.items():
            filepath = save_dir / f"{name}.png"
            figure.savefig(filepath, dpi=self.config.DPI, bbox_inches='tight', 
                          facecolor='white', edgecolor='none')
            print(f"  ✓ Сохранен: {filepath}")
    
    def _generate_summary(self, df: pd.DataFrame, alerts: Dict) -> Dict[str, Any]:
        """Генерация итоговой сводки"""
        summary = {
            'period_start': df['day_order'].min() if 'day_order' in df.columns else None,
            'period_end': df['day_order'].max() if 'day_order' in df.columns else None,
            'total_orders': df['cnt_order'].sum(),
            'avg_conversion': df['order2trip'].mean(),
            'avg_delays': df['delayed_ratio'].mean(),
            'cities_count': df['city'].nunique() if 'city' in df.columns else 0,
            'performance_rating': self._calculate_overall_rating(df),
            'alerts_summary': {
                'critical_count': len(alerts['critical']),
                'warning_count': len(alerts['warning']),
                'info_count': len(alerts['info'])
            }
        }
        
        return summary
    
    def _calculate_overall_rating(self, df: pd.DataFrame) -> str:
        """Расчет общего рейтинга производительности"""
        avg_conversion = df['order2trip'].mean()
        avg_delays = df['delayed_ratio'].mean()
        
        if avg_conversion >= 0.7 and avg_delays <= 0.15:
            return "Отлично"
        elif avg_conversion >= 0.5 and avg_delays <= 0.25:
            return "Хорошо"
        elif avg_conversion >= 0.4 and avg_delays <= 0.35:
            return "Удовлетворительно"
        else:
            return "Требует внимания"

# ============================================================================
# ПРИМЕР ИСПОЛЬЗОВАНИЯ
# ============================================================================
def demo_usage():
    # Генерация отчета
    config = EnhancedReportConfig()
    report_generator = EnhancedTaxiAnalyticsReport(config)
    
    result = report_generator.generate_comprehensive_report(
        df_test, 
        save_path="taxi_analytics_reports"
    )
    
    # Вывод сводки
    print("\n" + "="*80)
    print("📋 ИТОГОВАЯ СВОДКА ОТЧЕТА")
    print("="*80)
    
    summary = result['summary']
    print(f"📅 Период анализа: {summary['period_start']} - {summary['period_end']} день месяца")
    print(f"📊 Всего заказов: {summary['total_orders']:,}")
    print(f"🎯 Средняя конверсия: {summary['avg_conversion']):.1%}")
    print(f"⏱ Среднее время задержки: {summary['avg_delays']:.1%}")
    print(f"🏙️ Количество городов: {summary['cities_count']}")
    print(f"🏆 Общий рейтинг производительности: {summary['performance_rating']}")
    print(f"🔔 Количество критических алертов: {summary['alerts_summary']['critical_count']}")