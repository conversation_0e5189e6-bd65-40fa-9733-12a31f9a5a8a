import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import seaborn as sns
from typing import List, Dict, Tuple, Optional, Union
from datetime import timedelta
import warnings
from pathlib import Path
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import math
from matplotlib.gridspec import GridSpec

# ============================================================================
# КОНФИГУРАЦИЯ И НАСТРОЙКИ
# ============================================================================
class ReportConfig:
    """Централизованная конфигурация системы отчетности"""
    
    def __init__(self):
        self.THRESHOLD_DELAYED_MINUTES: int = 15
        self.OUTLIER_Z_SCORE: float = 2.5
        self.MIN_SAMPLES_FOR_OUTLIERS: int = 5
        
        self.METRICS: Dict[str, Tuple[str, Tuple[float, float], str]] = {
            'order2trip': ('Базовая конверсия Order→Trip', (0, 100), 'Общая эффективность системы'),
            'order2offer': ('Конверсия Order→Offer', (0, 100), 'Скорость формирования предложений'),
            'offer2assign': ('Конверсия Offer→Assign', (0, 100), 'Качество матчинга водителей'),
            'assign2arrive': ('Конверсия Assign→Arrive', (0, 100), 'Надежность водителей'),
            'arrive2trip': ('Конверсия Arrive→Trip', (0, 100), 'Финальная конверсия')
        }
        
        self.FIGURE_SIZE: Tuple[int, int] = (16, 10)
        self.DPI: int = 150
        
        self.COLORS_PALETTE = [
            '#2E86AB', '#A23B72', '#F18F01', '#C73E1D',
            '#6A994E', '#577590', '#F77F00', '#D62828',
            '#003049', '#669BBC', '#80ED99', '#F8F8FF'
        ]
        
        self.LINE_STYLES = ['-', '--', '-.', ':']
        self.MARKERS = ['o', 's', '^', 'v', 'D', 'P', '*', 'X']
        
        self.GRID_ALPHA: float = 0.2
        self.ANNOTATION_FONTSIZE: int = 9
        self.LEGEND_FONTSIZE: int = 10
        self.TITLE_FONTSIZE: int = 14
        self.LABEL_FONTSIZE: int = 11

# ============================================================================
# УТИЛИТЫ ДЛЯ ОБРАБОТКИ ДАННЫХ
# ============================================================================
class DataProcessor:
    """Упрощенный класс для обработки данных"""
    
    @staticmethod
    def validate_dataframe(df: pd.DataFrame) -> pd.DataFrame:
        """Базовая валидация"""
        if df.empty:
            raise ValueError("DataFrame пуст")
        
        df = df.drop_duplicates(subset=['id_order'])
        return df
    
    @staticmethod
    def parse_datetime_columns(df: pd.DataFrame) -> pd.DataFrame:
        """Упрощенный парсинг дат"""
        df = df.copy()
        datetime_cols = ['order_time', 'offer_time', 'assign_time', 'arrive_time', 'trip_time']
        
        for col in datetime_cols:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')
        
        return df
    
    @staticmethod
    def detect_delayed_orders(df: pd.DataFrame, threshold_minutes: int = 15) -> pd.DataFrame:
        """Упрощенная детекция отложенных заказов"""
        df = df.copy()
        time_diff = (df['offer_time'] - df['order_time']).dt.total_seconds() / 60
        df['is_delayed'] = (time_diff > threshold_minutes).astype(int)
        df['delay_minutes'] = time_diff.fillna(0)
        return df

class MetricsCalculator:
    """Упрощенный расчет метрик"""
    
    @staticmethod
    def calculate_conversion_metrics(df: pd.DataFrame, group_cols: List[str]) -> pd.DataFrame:
        """Расчет метрик конверсии с исправлением конфликта колонок"""
        
        df_work = df.copy()
        
        agg_dict = {
            'id_order': 'count',
            'offer_time': lambda x: x.notna().sum(),
            'assign_time': lambda x: x.notna().sum(),
            'arrive_time': lambda x: x.notna().sum(),
            'trip_time': lambda x: x.notna().sum(),
            'is_delayed': 'sum',
            'delay_minutes': 'mean'
        }
        
        result = df_work.groupby(group_cols).agg(agg_dict)
        result.columns = ['cnt_order', 'cnt_offer', 'cnt_assign', 'cnt_arrive', 'cnt_trip', 'cnt_delayed', 'avg_delay_minutes']
        result = result.reset_index()
        
        result['order2trip'] = np.where(result['cnt_order'] > 0, result['cnt_trip'] / result['cnt_order'], 0)
        result['order2offer'] = np.where(result['cnt_order'] > 0, result['cnt_offer'] / result['cnt_order'], 0)
        result['offer2assign'] = np.where(result['cnt_offer'] > 0, result['cnt_assign'] / result['cnt_offer'], 0)
        result['assign2arrive'] = np.where(result['cnt_assign'] > 0, result['cnt_arrive'] / result['cnt_assign'], 0)
        result['arrive2trip'] = np.where(result['cnt_arrive'] > 0, result['cnt_trip'] / result['cnt_arrive'], 0)
        result['delayed_ratio'] = np.where(result['cnt_order'] > 0, result['cnt_delayed'] / result['cnt_order'], 0)
        
        return result

# ============================================================================
# УЛУЧШЕННАЯ СИСТЕМА ВИЗУАЛИЗАЦИИ
# ============================================================================
class ModernVisualizer:
    """Современная система визуализации с улучшенным дизайном"""

    def __init__(self, config: ReportConfig):
        self.config = config
        self.setup_style()

    def setup_style(self):
        """Настройка современного стиля"""
        plt.style.use('default')

        custom_style = {
            'figure.figsize': self.config.FIGURE_SIZE,
            'figure.dpi': self.config.DPI,
            'figure.facecolor': 'white',
            'axes.facecolor': '#f8f9fa',
            'axes.edgecolor': '#dee2e6',
            'axes.linewidth': 1,
            'axes.grid': True,
            'axes.axisbelow': True,
            'grid.color': '#e9ecef',
            'grid.alpha': self.config.GRID_ALPHA,
            'axes.titlesize': self.config.TITLE_FONTSIZE,
            'axes.labelsize': self.config.LABEL_FONTSIZE,
            'xtick.labelsize': 10,
            'ytick.labelsize': 10,
            'legend.fontsize': self.config.LEGEND_FONTSIZE,
            'legend.frameon': True,
            'legend.fancybox': True,
            'legend.shadow': False,
            'legend.framealpha': 0.9,
            'axes.spines.top': False,
            'axes.spines.right': False,
            'axes.spines.left': True,
            'axes.spines.bottom': True,
        }

        plt.rcParams.update(custom_style)

    def create_comprehensive_dashboard(self, df: pd.DataFrame, alerts: Dict, insights: Dict) -> plt.Figure:
        """Создание единого комплексного дашборда без дублирования"""

        fig = plt.figure(figsize=(24, 16))
        gs = GridSpec(5, 6, figure=fig, hspace=0.4, wspace=0.3)

        # Верхняя панель - KPI карточки
        self._create_enhanced_kpi_cards(fig, df, gs)

        # Основные графики
        self._create_funnel_analysis(fig, df, gs)
        self._create_performance_matrix(fig, df, gs)
        self._create_time_analysis(fig, df, gs)

        # Нижняя панель - инсайты и рекомендации
        self._create_actionable_insights_panel(fig, df, alerts, insights, gs)

        plt.suptitle('📊 Комплексный аналитический дашборд - Такси сервис',
                    fontsize=22, fontweight='bold', y=0.98)

        return fig
    
    def _create_enhanced_kpi_cards(self, fig, df, gs):
        """Создание улучшенных KPI карточек с трендами"""
        kpis = [
            ("Общая конверсия", df['order2trip'].mean(), "%", "order2trip"),
            ("Всего заказов", df['cnt_order'].sum(), "", "cnt_order"),
            ("Доля отложенных", df['delayed_ratio'].mean(), "%", "delayed_ratio"),
            ("Среднее время задержки", df['avg_delay_minutes'].mean(), "мин", "avg_delay_minutes"),
            ("Лучший город", df.groupby('city')['order2trip'].mean().idxmax(), "", None),
            ("Проблемный город", df.groupby('city')['delayed_ratio'].mean().idxmax(), "", None)
        ]

        for i, (title, value, unit, metric) in enumerate(kpis):
            ax = fig.add_subplot(gs[0, i])
            ax.axis('off')

            # Определение цвета на основе значения
            if "конверсия" in title.lower():
                color = '#28a745' if value > 0.4 else '#dc3545' if value < 0.3 else '#ffc107'
            elif "отложенных" in title.lower():
                color = '#28a745' if value < 0.3 else '#dc3545' if value > 0.6 else '#ffc107'
            elif "проблемный" in title.lower():
                color = '#dc3545'
            elif "лучший" in title.lower():
                color = '#28a745'
            else:
                color = '#17a2b8'

            # Форматирование значения
            if unit == "%":
                display_value = f"{value:.1%}"
            elif unit == "" and isinstance(value, (int, float)) and value > 1000:
                display_value = f"{value:,.0f}"
            elif unit == "" and isinstance(value, str):
                display_value = value
            else:
                display_value = f"{value:.1f}{unit}"

            # Основное значение
            ax.text(0.5, 0.7, display_value, ha='center', va='center',
                   fontsize=20, fontweight='bold', color=color, transform=ax.transAxes)

            # Заголовок
            ax.text(0.5, 0.3, title, ha='center', va='center',
                   fontsize=11, transform=ax.transAxes, wrap=True)

            # Мини-тренд для метрик с данными
            if metric and metric in df.columns:
                trend_data = df.groupby('day_order')[metric].mean()
                if len(trend_data) > 1:
                    mini_ax = ax.inset_axes([0.1, 0.05, 0.8, 0.2])
                    mini_ax.plot(trend_data.index, trend_data.values, color=color, linewidth=2)
                    mini_ax.set_xticks([])
                    mini_ax.set_yticks([])
                    mini_ax.spines['top'].set_visible(False)
                    mini_ax.spines['right'].set_visible(False)
                    mini_ax.spines['bottom'].set_visible(False)
                    mini_ax.spines['left'].set_visible(False)

            # Рамка
            ax.add_patch(Rectangle((0.05, 0.1), 0.9, 0.8,
                                 linewidth=2, edgecolor=color, facecolor='none',
                                 transform=ax.transAxes))
    
    def _create_funnel_analysis(self, fig, df, gs):
        """Создание анализа воронки конверсии"""
        ax = fig.add_subplot(gs[1, :3])

        # Расчет средних значений по всем городам
        funnel_metrics = ['order2offer', 'offer2assign', 'assign2arrive', 'arrive2trip']
        funnel_names = ['Order→Offer', 'Offer→Assign', 'Assign→Arrive', 'Arrive→Trip']

        avg_values = []
        for metric in funnel_metrics:
            if metric in df.columns:
                avg_values.append(df[metric].mean())
            else:
                avg_values.append(0)

        # Создание воронки
        colors = ['#2E86AB', '#A23B72', '#F18F01', '#6A994E']
        y_pos = range(len(funnel_names))

        bars = ax.barh(y_pos, [v * 100 for v in avg_values], color=colors, alpha=0.8)

        # Добавление значений на бары
        for i, (bar, value) in enumerate(zip(bars, avg_values)):
            width = bar.get_width()
            ax.text(width + 1, bar.get_y() + bar.get_height()/2,
                   f'{value:.1%}', ha='left', va='center', fontweight='bold')

        ax.set_yticks(y_pos)
        ax.set_yticklabels(funnel_names)
        ax.set_xlabel('Конверсия (%)')
        ax.set_title('Воронка конверсии (средние значения)', fontweight='bold', pad=15)
        ax.set_xlim(0, 110)

        # Добавление вертикальных линий для бенчмарков
        ax.axvline(x=80, color='green', linestyle='--', alpha=0.7, label='Хорошо (80%)')
        ax.axvline(x=60, color='orange', linestyle='--', alpha=0.7, label='Удовлетворительно (60%)')
        ax.axvline(x=40, color='red', linestyle='--', alpha=0.7, label='Требует внимания (40%)')
        ax.legend(loc='lower right')
        ax.grid(True, alpha=0.3, axis='x')

    def _create_performance_matrix(self, fig, df, gs):
        """Создание матрицы производительности городов"""
        ax = fig.add_subplot(gs[1, 3:])

        cities = sorted(df['city'].unique())

        # Подготовка данных для heatmap
        city_stats = df.groupby('city').agg({
            'order2trip': 'mean',
            'delayed_ratio': 'mean',
            'cnt_order': 'sum',
            'avg_delay_minutes': 'mean'
        })

        # Нормализация данных (инвертируем негативные метрики)
        normalized_stats = city_stats.copy()
        normalized_stats['order2trip'] = city_stats['order2trip']
        normalized_stats['delayed_ratio'] = 1 - city_stats['delayed_ratio']
        normalized_stats['cnt_order'] = city_stats['cnt_order'] / city_stats['cnt_order'].max()
        normalized_stats['avg_delay_minutes'] = 1 - (city_stats['avg_delay_minutes'] / city_stats['avg_delay_minutes'].max())

        # Создание heatmap
        metric_names = ['Конверсия', 'Качество\nсервиса', 'Объем\nзаказов', 'Скорость\nобслуживания']
        data_for_heatmap = normalized_stats.T
        data_for_heatmap.index = metric_names

        im = ax.imshow(data_for_heatmap.values, cmap='RdYlGn', aspect='auto', vmin=0, vmax=1)

        # Настройка осей
        ax.set_xticks(range(len(cities)))
        ax.set_xticklabels(cities, rotation=45)
        ax.set_yticks(range(len(metric_names)))
        ax.set_yticklabels(metric_names)

        # Добавление значений в ячейки
        for i in range(len(metric_names)):
            for j, city in enumerate(cities):
                if i == 0:  # Конверсия
                    value = f"{city_stats.iloc[j, 0]:.1%}"
                elif i == 1:  # Качество сервиса (инвертированная доля отложенных)
                    value = f"{(1-city_stats.iloc[j, 1]):.1%}"
                elif i == 2:  # Объем заказов
                    value = f"{city_stats.iloc[j, 2]:,.0f}"
                else:  # Скорость обслуживания
                    value = f"{city_stats.iloc[j, 3]:.1f}м"

                ax.text(j, i, value, ha='center', va='center',
                       fontweight='bold', fontsize=10,
                       color='white' if normalized_stats.iloc[i, j] < 0.5 else 'black')

        ax.set_title('Матрица производительности по городам', fontweight='bold', pad=15)

        # Добавление цветовой шкалы
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Нормализованная производительность', rotation=270, labelpad=20)
    
    def _create_time_analysis(self, fig, df, gs):
        """Создание временного анализа"""
        # График 1: Динамика ключевых метрик
        ax1 = fig.add_subplot(gs[2, :3])

        trend_data = df.groupby('day_order').agg({
            'order2trip': 'mean',
            'delayed_ratio': 'mean',
            'cnt_order': 'sum'
        })

        # Основная ось - конверсия
        line1 = ax1.plot(trend_data.index, trend_data['order2trip'] * 100,
                        color='#2E86AB', linewidth=3, marker='o', markersize=6,
                        label='Базовая конверсия (%)')

        # Вторичная ось - доля отложенных
        ax2 = ax1.twinx()
        line2 = ax2.plot(trend_data.index, trend_data['delayed_ratio'] * 100,
                        color='#A23B72', linewidth=3, marker='s', markersize=6,
                        label='Доля отложенных (%)')

        # Настройка осей
        ax1.set_xlabel('День месяца', fontweight='bold')
        ax1.set_ylabel('Базовая конверсия (%)', color='#2E86AB', fontweight='bold')
        ax2.set_ylabel('Доля отложенных (%)', color='#A23B72', fontweight='bold')

        # Объединенная легенда
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='upper left')

        ax1.set_title('Динамика ключевых метрик по дням', fontweight='bold', pad=15)
        ax1.grid(True, alpha=0.3)

        # Выделение выходных дней
        weekend_days = [6, 7, 13, 14, 20, 21, 27, 28]
        for day in weekend_days:
            if day in trend_data.index:
                ax1.axvspan(day-0.5, day+0.5, alpha=0.1, color='red', zorder=0)

        # График 2: Объем заказов по дням недели
        ax3 = fig.add_subplot(gs[2, 3:])

        # Добавляем день недели если его нет
        if 'weekday' not in df.columns:
            df['weekday'] = df['day_order'] % 7

        weekday_data = df.groupby('weekday').agg({
            'cnt_order': 'sum',
            'order2trip': 'mean'
        })

        weekday_names = ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Вс']

        bars = ax3.bar(range(7), weekday_data['cnt_order'],
                      color=['#2E86AB' if i < 5 else '#A23B72' for i in range(7)],
                      alpha=0.8)

        ax3.set_xticks(range(7))
        ax3.set_xticklabels(weekday_names)
        ax3.set_ylabel('Количество заказов')
        ax3.set_title('Распределение заказов по дням недели', fontweight='bold', pad=15)

        # Добавление значений на бары
        for bar in bars:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{height:,.0f}', ha='center', va='bottom', fontweight='bold', fontsize=9)

        ax3.grid(True, alpha=0.3, axis='y')
    
    def _create_actionable_insights_panel(self, fig, df, alerts, insights, gs):
        """Создание панели с практическими инсайтами и рекомендациями"""

        # Левая часть - ключевые инсайты
        ax1 = fig.add_subplot(gs[3:, :3])
        ax1.axis('off')

        # Генерация улучшенных инсайтов
        enhanced_insights = self._generate_enhanced_insights(df)

        insight_text = "💡 КЛЮЧЕВЫЕ ИНСАЙТЫ И РЕКОМЕНДАЦИИ\n\n"

        for i, insight in enumerate(enhanced_insights[:6], 1):
            insight_text += f"{i}. {insight}\n\n"

        ax1.text(0.05, 0.95, insight_text, transform=ax1.transAxes,
               fontsize=12, verticalalignment='top',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='#e8f4fd',
                        edgecolor='#2E86AB', alpha=0.9))

        # Правая часть - алерты и статус
        ax2 = fig.add_subplot(gs[3:, 3:])
        ax2.axis('off')

        # Система мониторинга
        alert_text = "🚨 СИСТЕМА МОНИТОРИНГА\n\n"

        # Статистика по алертам
        total_critical = len(alerts.get('critical', []))
        total_warning = len(alerts.get('warning', []))
        total_info = len(alerts.get('info', []))

        if total_critical > 0:
            alert_text += f"🔴 КРИТИЧНО: {total_critical} проблем\n"
            for alert in alerts['critical'][:3]:  # Показываем только топ-3
                alert_text += f"   • {alert['message']}\n"
            if len(alerts['critical']) > 3:
                alert_text += f"   • ... и еще {len(alerts['critical']) - 3} проблем\n"
            alert_text += "\n"

        if total_warning > 0:
            alert_text += f"🟡 ВНИМАНИЕ: {total_warning} предупреждений\n"
            for alert in alerts['warning'][:2]:  # Показываем только топ-2
                alert_text += f"   • {alert['message']}\n"
            if len(alerts['warning']) > 2:
                alert_text += f"   • ... и еще {len(alerts['warning']) - 2} предупреждений\n"
            alert_text += "\n"

        if total_critical == 0 and total_warning == 0:
            alert_text += "✅ Критических проблем не обнаружено\n\n"

        # Добавляем общую оценку системы
        overall_score = self._calculate_overall_score(df)
        alert_text += f"📊 ОБЩАЯ ОЦЕНКА СИСТЕМЫ\n"
        alert_text += f"Балл: {overall_score:.1f}/10\n"
        alert_text += f"Статус: {self._get_status_by_score(overall_score)}\n\n"

        # Следующие шаги
        alert_text += "🎯 ПРИОРИТЕТНЫЕ ДЕЙСТВИЯ\n"
        next_steps = self._generate_next_steps(df, alerts)
        for i, step in enumerate(next_steps[:3], 1):
            alert_text += f"{i}. {step}\n"

        ax2.text(0.05, 0.95, alert_text, transform=ax2.transAxes,
               fontsize=12, verticalalignment='top',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='#fff3cd',
                        edgecolor='#ffc107', alpha=0.9))
    
    def _generate_enhanced_insights(self, df):
        """Генерация улучшенных инсайтов с практическими рекомендациями"""
        insights = []

        # Анализ производительности городов
        city_performance = df.groupby('city').agg({
            'order2trip': 'mean',
            'delayed_ratio': 'mean',
            'cnt_order': 'sum',
            'avg_delay_minutes': 'mean'
        }).round(3)

        best_city = city_performance['order2trip'].idxmax()
        worst_city = city_performance['order2trip'].idxmin()
        best_conv = city_performance.loc[best_city, 'order2trip']
        worst_conv = city_performance.loc[worst_city, 'order2trip']

        insights.append(f"Лидер по конверсии: {best_city} ({best_conv:.1%}). "
                       f"Изучите их практики для тиражирования в другие города.")

        if worst_conv < 0.3:
            insights.append(f"Критическая ситуация в {worst_city} ({worst_conv:.1%}). "
                           f"Требуется немедленное вмешательство и анализ причин.")

        # Анализ задержек
        high_delay_cities = city_performance[city_performance['delayed_ratio'] > 0.5]
        if not high_delay_cities.empty:
            city_list = ', '.join(high_delay_cities.index)
            insights.append(f"Высокий уровень задержек в городах: {city_list}. "
                           f"Рекомендуется увеличить количество водителей или оптимизировать алгоритм матчинга.")

        # Анализ объемов
        total_orders = df['cnt_order'].sum()
        top_volume_city = city_performance['cnt_order'].idxmax()
        top_volume = city_performance.loc[top_volume_city, 'cnt_order']
        volume_share = (top_volume / total_orders) * 100

        insights.append(f"Основной объем ({volume_share:.1f}%) приходится на {top_volume_city}. "
                       f"Обеспечьте достаточные ресурсы для поддержания качества сервиса.")

        # Временной анализ
        daily_trend = df.groupby('day_order')['order2trip'].mean()
        if len(daily_trend) > 1:
            trend_direction = "растет" if daily_trend.iloc[-1] > daily_trend.iloc[0] else "снижается"
            trend_change = abs(daily_trend.iloc[-1] - daily_trend.iloc[0]) * 100
            insights.append(f"Конверсия {trend_direction} на {trend_change:.1f}п.п. за период. "
                           f"{'Поддерживайте текущую стратегию' if trend_direction == 'растет' else 'Требуется анализ причин снижения'}.")

        # Анализ эффективности воронки
        funnel_metrics = ['order2offer', 'offer2assign', 'assign2arrive', 'arrive2trip']
        funnel_values = []
        for metric in funnel_metrics:
            if metric in df.columns:
                funnel_values.append(df[metric].mean())

        if funnel_values:
            min_step_idx = funnel_values.index(min(funnel_values))
            step_names = ['формирования предложений', 'назначения водителя', 'прибытия водителя', 'начала поездки']
            insights.append(f"Узкое место воронки: этап {step_names[min_step_idx]} ({min(funnel_values):.1%}). "
                           f"Сосредоточьте усилия на улучшении этого этапа.")

        return insights
    
    def _calculate_overall_score(self, df):
        """Расчет общей оценки системы"""
        # Базовые веса для метрик
        weights = {
            'conversion': 0.4,  # Конверсия - самое важное
            'delays': 0.3,      # Задержки
            'volume': 0.2,      # Объем
            'stability': 0.1    # Стабильность
        }

        # Расчет компонентов оценки
        avg_conversion = df['order2trip'].mean()
        avg_delays = 1 - df['delayed_ratio'].mean()  # Инвертируем (меньше задержек = лучше)
        volume_score = min(df['cnt_order'].sum() / 10000, 1)  # Нормализуем объем

        # Стабильность (обратная величина стандартного отклонения конверсии)
        conversion_std = df.groupby('day_order')['order2trip'].mean().std()
        stability_score = max(0, 1 - conversion_std * 10)

        # Итоговая оценка
        total_score = (
            avg_conversion * weights['conversion'] +
            avg_delays * weights['delays'] +
            volume_score * weights['volume'] +
            stability_score * weights['stability']
        ) * 10

        return total_score

    def _get_status_by_score(self, score):
        """Получение статуса по оценке"""
        if score >= 8:
            return "🟢 Отлично"
        elif score >= 6:
            return "🟡 Хорошо"
        elif score >= 4:
            return "🟠 Удовлетворительно"
        else:
            return "🔴 Требует внимания"

    def _generate_next_steps(self, df, alerts):
        """Генерация следующих шагов"""
        steps = []

        # На основе алертов
        if alerts.get('critical'):
            steps.append("Устранить критические проблемы с конверсией в проблемных городах")

        if alerts.get('warning'):
            steps.append("Оптимизировать процесс обработки заказов для снижения задержек")

        # На основе анализа данных
        city_performance = df.groupby('city')['order2trip'].mean()
        if city_performance.min() < 0.3:
            steps.append("Провести детальный анализ причин низкой конверсии в худших городах")

        delayed_ratio = df['delayed_ratio'].mean()
        if delayed_ratio > 0.5:
            steps.append("Увеличить количество водителей или улучшить алгоритм матчинга")

        # Общие рекомендации
        if len(steps) == 0:
            steps.append("Продолжить мониторинг ключевых метрик")
            steps.append("Тиражировать лучшие практики из успешных городов")
            steps.append("Провести A/B тестирование новых улучшений")

        return steps[:3]  # Возвращаем только топ-3
    


# ============================================================================
# УЛУЧШЕННАЯ СИСТЕМА АЛЕРТОВ
# ============================================================================
class EnhancedAnomalyDetector:
    """Улучшенная система детекции аномалий с контекстом"""

    @staticmethod
    def generate_comprehensive_alerts(df: pd.DataFrame, config: ReportConfig) -> Dict[str, List[Dict]]:
        """Генерация комплексных алертов с контекстом и рекомендациями"""
        alerts = {'critical': [], 'warning': [], 'info': []}

        # Критические алерты
        low_conversion = df[df['order2trip'] < 0.2]
        for _, row in low_conversion.iterrows():
            volume = row['cnt_order']
            context = f" (объем: {volume:,} заказов)" if volume > 100 else " (низкий объем)"
            alerts['critical'].append({
                'message': f"Критически низкая конверсия в {row['city']}: {row['order2trip']:.1%}{context}",
                'recommendation': "Немедленно проанализировать причины и принять меры"
            })

        # Предупреждения
        medium_conversion = df[(df['order2trip'] >= 0.2) & (df['order2trip'] < 0.4)]
        for _, row in medium_conversion.iterrows():
            alerts['warning'].append({
                'message': f"Низкая конверсия в {row['city']}: {row['order2trip']:.1%}",
                'recommendation': "Рассмотреть возможности улучшения"
            })

        high_delayed = df[df['delayed_ratio'] > 0.6]
        for _, row in high_delayed.iterrows():
            avg_delay = row['avg_delay_minutes']
            alerts['warning'].append({
                'message': f"Высокая доля отложенных в {row['city']}: {row['delayed_ratio']:.1%} (среднее время: {avg_delay:.1f}мин)",
                'recommendation': "Оптимизировать процесс матчинга водителей"
            })

        # Информационные алерты
        city_performance = df.groupby('city')['order2trip'].mean()
        if len(city_performance) > 1:
            performance_gap = city_performance.max() - city_performance.min()
            if performance_gap > 0.3:
                best_city = city_performance.idxmax()
                worst_city = city_performance.idxmin()
                alerts['info'].append({
                    'message': f"Большой разрыв в производительности между городами: {performance_gap:.1%}",
                    'recommendation': f"Изучить опыт {best_city} для применения в {worst_city}"
                })

        return alerts

    @staticmethod
    def generate_insights_data(df: pd.DataFrame) -> Dict:
        """Генерация данных для инсайтов"""
        insights_data = {}

        # Анализ трендов
        daily_trend = df.groupby('day_order')['order2trip'].mean()
        if len(daily_trend) > 1:
            trend_slope = (daily_trend.iloc[-1] - daily_trend.iloc[0]) / len(daily_trend)
            insights_data['trend_direction'] = 'positive' if trend_slope > 0 else 'negative'
            insights_data['trend_magnitude'] = abs(trend_slope)

        # Лучшие и худшие исполнители
        city_stats = df.groupby('city').agg({
            'order2trip': 'mean',
            'delayed_ratio': 'mean',
            'cnt_order': 'sum'
        })

        insights_data['best_performer'] = city_stats['order2trip'].idxmax()
        insights_data['worst_performer'] = city_stats['order2trip'].idxmin()
        insights_data['highest_volume'] = city_stats['cnt_order'].idxmax()

        # Общие статистики
        insights_data['total_orders'] = df['cnt_order'].sum()
        insights_data['avg_conversion'] = df['order2trip'].mean()
        insights_data['avg_delays'] = df['delayed_ratio'].mean()

        return insights_data

# ============================================================================
# ГЛАВНЫЙ КЛАСС
# ============================================================================
class TaxiReportGenerator:
    """Улучшенный главный класс с комплексной аналитикой"""

    def __init__(self, config: Optional[ReportConfig] = None):
        self.config = config or ReportConfig()
        self.visualizer = ModernVisualizer(self.config)
        self.anomaly_detector = EnhancedAnomalyDetector()
        
    def load_and_process_data(self, filepath: Union[str, Path]) -> pd.DataFrame:
        """Загрузка и обработка данных"""
        df = pd.read_excel(filepath)
        df = DataProcessor.validate_dataframe(df)
        df = DataProcessor.parse_datetime_columns(df)
        df = DataProcessor.detect_delayed_orders(df, self.config.THRESHOLD_DELAYED_MINUTES)
        
        df['day_order'] = df['order_time'].dt.day
        df['hour_order'] = df['order_time'].dt.hour
        df['weekday'] = df['order_time'].dt.weekday
        
        return df
    
    def generate_comprehensive_report(self, df: pd.DataFrame,
                                    cities: Optional[List[str]] = None,
                                    save_plots: bool = False) -> Dict:
        """Генерация комплексного отчета без дублирования"""

        if cities is None:
            cities = sorted(df['city'].unique())

        df_filtered = df[df['city'].isin(cities)].copy()

        # Расчет метрик
        df_metrics = MetricsCalculator.calculate_conversion_metrics(
            df_filtered, ['day_order', 'city', 'is_delayed']
        )

        # Генерация алертов и инсайтов
        alerts = self.anomaly_detector.generate_comprehensive_alerts(df_metrics, self.config)
        insights_data = self.anomaly_detector.generate_insights_data(df_metrics)

        # Создание единого комплексного дашборда
        dashboard = self.visualizer.create_comprehensive_dashboard(df_metrics, alerts, insights_data)

        if save_plots:
            dashboard.savefig('comprehensive_taxi_dashboard.png',
                           dpi=300, bbox_inches='tight', facecolor='white')
            print("📊 Дашборд сохранен как 'comprehensive_taxi_dashboard.png'")

        plt.show()

        return {
            'data': df_metrics,
            'alerts': alerts,
            'insights': insights_data,
            'dashboard': dashboard,
            'summary_stats': self._generate_enhanced_summary_stats(df_metrics, alerts, insights_data)
        }
    
    def _generate_enhanced_summary_stats(self, df: pd.DataFrame, alerts: Dict, insights: Dict) -> Dict:
        """Генерация улучшенной сводной статистики"""

        city_stats = df.groupby('city').agg({
            'order2trip': 'mean',
            'delayed_ratio': 'mean',
            'cnt_order': 'sum',
            'avg_delay_minutes': 'mean'
        }).round(3)

        # Общая статистика
        total_stats = {
            'total_orders': df['cnt_order'].sum(),
            'avg_conversion': df['order2trip'].mean(),
            'delayed_ratio': df['delayed_ratio'].mean(),
            'avg_delay_minutes': df['avg_delay_minutes'].mean(),
            'cities_count': len(df['city'].unique())
        }

        # Рейтинг городов
        city_ranking = {
            'best_conversion': city_stats['order2trip'].idxmax(),
            'worst_conversion': city_stats['order2trip'].idxmin(),
            'highest_volume': city_stats['cnt_order'].idxmax(),
            'lowest_delays': city_stats['delayed_ratio'].idxmin(),
            'highest_delays': city_stats['delayed_ratio'].idxmax()
        }

        # Статистика алертов
        alert_summary = {
            'critical_count': len(alerts.get('critical', [])),
            'warning_count': len(alerts.get('warning', [])),
            'info_count': len(alerts.get('info', [])),
            'total_issues': len(alerts.get('critical', [])) + len(alerts.get('warning', []))
        }

        # Ключевые инсайты
        key_insights = {
            'performance_gap': city_stats['order2trip'].max() - city_stats['order2trip'].min(),
            'delay_variation': city_stats['delayed_ratio'].std(),
            'volume_concentration': city_stats['cnt_order'].max() / city_stats['cnt_order'].sum(),
            'overall_health': 'good' if alert_summary['critical_count'] == 0 else 'needs_attention'
        }

        return {
            'total_stats': total_stats,
            'city_ranking': city_ranking,
            'alert_summary': alert_summary,
            'key_insights': key_insights,
            'detailed_by_city': city_stats.to_dict()
        }

# ============================================================================
# ОСНОВНАЯ ФУНКЦИЯ
# ============================================================================
def main():
    """Основная функция для запуска улучшенного анализа"""

    config = ReportConfig()
    report_generator = TaxiReportGenerator(config)

    try:
        print("🔄 Загрузка и обработка данных...")
        df = report_generator.load_and_process_data('taxi_data.xlsx')

        print("📊 Генерация комплексного отчета...")
        results = report_generator.generate_comprehensive_report(
            df,
            cities=None,
            save_plots=True
        )

        print("\n" + "="*80)
        print("📊 КОМПЛЕКСНЫЙ АНАЛИТИЧЕСКИЙ ОТЧЕТ")
        print("="*80)

        stats = results['summary_stats']

        # Общая статистика
        print(f"\n📈 ОБЩИЕ ПОКАЗАТЕЛИ:")
        print("-" * 50)
        total = stats['total_stats']
        print(f"Общее количество заказов: {total['total_orders']:,}")
        print(f"Средняя конверсия: {total['avg_conversion']:.1%}")
        print(f"Доля отложенных заказов: {total['delayed_ratio']:.1%}")
        print(f"Среднее время задержки: {total['avg_delay_minutes']:.1f} минут")
        print(f"Количество городов: {total['cities_count']}")

        # Рейтинг городов
        print(f"\n🏆 РЕЙТИНГ ГОРОДОВ:")
        print("-" * 50)
        ranking = stats['city_ranking']
        print(f"🥇 Лучшая конверсия: {ranking['best_conversion']}")
        print(f"🥉 Худшая конверсия: {ranking['worst_conversion']}")
        print(f"📊 Наибольший объем: {ranking['highest_volume']}")
        print(f"⚡ Наименьшие задержки: {ranking['lowest_delays']}")
        print(f"🐌 Наибольшие задержки: {ranking['highest_delays']}")

        # Статистика проблем
        print(f"\n🚨 МОНИТОРИНГ ПРОБЛЕМ:")
        print("-" * 50)
        alert_summary = stats['alert_summary']
        print(f"🔴 Критических проблем: {alert_summary['critical_count']}")
        print(f"🟡 Предупреждений: {alert_summary['warning_count']}")
        print(f"🔵 Информационных: {alert_summary['info_count']}")
        print(f"📊 Общий статус: {'✅ Система работает стабильно' if alert_summary['total_issues'] == 0 else f'⚠️ Требует внимания ({alert_summary[\"total_issues\"]} проблем)'}")

        # Ключевые инсайты
        print(f"\n💡 КЛЮЧЕВЫЕ ИНСАЙТЫ:")
        print("-" * 50)
        insights = stats['key_insights']
        print(f"Разрыв в производительности: {insights['performance_gap']:.1%}")
        print(f"Вариация задержек: {insights['delay_variation']:.1%}")
        print(f"Концентрация объема: {insights['volume_concentration']:.1%}")
        print(f"Общее состояние: {'🟢 Хорошее' if insights['overall_health'] == 'good' else '🔴 Требует внимания'}")

        # Детальные алерты
        alerts = results['alerts']
        if any(alerts.values()):
            print(f"\n🚨 ДЕТАЛЬНЫЕ АЛЕРТЫ:")
            print("-" * 50)

            for level, alert_list in alerts.items():
                if alert_list:
                    level_emoji = {'critical': '🔴', 'warning': '🟡', 'info': '🔵'}
                    print(f"\n{level_emoji[level]} {level.upper()}:")
                    for alert in alert_list:
                        print(f"  • {alert['message']}")
                        if 'recommendation' in alert:
                            print(f"    💡 {alert['recommendation']}")

        print("\n" + "="*80)
        print("✅ Комплексный анализ завершен!")
        print("📊 Единый дашборд создан и сохранен.")
        print("🎯 Все ключевые метрики объединены без дублирования.")
        print("="*80)

    except FileNotFoundError:
        print("❌ Файл 'taxi_data.xlsx' не найден!")
        print("Убедитесь, что файл находится в текущей директории.")

    except Exception as e:
        print(f"❌ Произошла ошибка: {str(e)}")
        print("Проверьте формат данных и попробуйте еще раз.")

if __name__ == "__main__":
    main()